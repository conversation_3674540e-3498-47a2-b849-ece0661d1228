import CryptoJS from "crypto-js";

export function parseQuery(search: string): Record<string, string> {
  return search
    .substring(1)
    .split("&")
    .reduce(
      (acc, pair) => {
        const [key, value] = pair.split("=");
        if (key) acc[decodeURIComponent(key)] = decodeURIComponent(value || "");
        return acc;
      },
      {} as Record<string, string>
    );
}

/**
 * 获取传入值的数据类型
 * @param {*} val 传入的值
 * @returns
 */
export function getTag(val: any) {
  if (val == null) return val === undefined ? "[object Undefined]" : "[object Null]";
  return Object.prototype.toString.call(val);
}

/**
 * 判断是否为null 包括 null undefined 'null' 'undefined' 'NULL' 'UNDEFINED'
 * @param {*} val 要判断的值
 * @returns {Boolean}
 */
export function isNull(val: unknown) {
  return !!("" + val).trim().match(/^(null)$|^(undefined)$/i);
}

/**
 * 判断是否为空 包括 null undefined 'null' 'undefined' 'NULL' 'UNDEFINED' '' {} '{}' [] '[]'
 * @param {*} val 要判断的值
 * @returns {Boolean}
 */
export function isEmpty(val: unknown) {
  if (isNull(val)) {
    return true;
  } else if (getTag(val) === "[object Date]") {
    const date: Date = val as Date;
    return isNaN(new Date(date).valueOf());
  } else if (getTag(val) === "[object Object]" && val) {
    return Object.keys(val).length === 0;
  } else {
    val = ("" + val).trim();
    return val === "" || val === "{}" || val === "[]";
  }
}

/**
 * 判断是否是真，仅当'false'、false、'0'、0和空值(isNull)时返回false
 * @param {Boolean} bool 要判断的值
 * @returns {Boolean}
 */
export function isTrue(bool: boolean | string | number) {
  return !!bool && !(bool === "false" || bool === "0" || isNull(bool));
}

/**
 * 验签过滤入参
 * 规则 非空 非0 非 false的值
 */
export function filterData(val: Record<string, unknown>): Record<string, unknown> {
  const keys = Object.keys(val).filter((key) => !isEmpty(val[key]) && isTrue(val[key] as string));
  return keys.reduce((pre: Record<string, unknown>, cur) => {
    pre[cur] = val[cur];
    return pre;
  }, {});
}

/**
 * 根据url生成query对象（目前暂时只支持单层级）
 */
export function getQuery(val: string = ""): Record<string, string> {
  // 转译
  val = decodeURIComponent(val);
  const list: Array<string> = val.replace("?", "").split("&");
  return list.reduce((pre: Record<string, string>, cur) => {
    const array = cur.split("=");
    if (array.length < 2) return pre;
    pre[array[0]] = array[1];
    return pre;
  }, {});
}

/**
 * 验签加密
 * @param data
 * @param terminal
 * @returns
 */
// export function signEncrypt(data: Record<string, unknown> = {}, terminal: number):string {
//   const newData: Record<string, unknown> = JSON.parse(JSON.stringify(data))
//   let str = Object.keys(newData).sort().reduce((pre, cur, index) => {
//     const value = newData[cur] as string
//     if (index === 0) {
//       pre = value
//     } else {
//       pre += ('||' + value.toString())
//     }
//     return pre
//   }, '')
//   str += `||${terminal}||${Math.floor(Date.now() / 1000)}||${import.meta.env.VITE_SIGN_KEY}`
//   return Md5(str).toString()
// }
export const md5 = (str: string): string => {
  return CryptoJS.MD5(str).toString();
};
/**
 * 签名加密
 */
export const signEncrypt = (data: Record<string, unknown>): string => {
  const signkey = import.meta.env.VITE_SIGN_KEY;
  // 过滤掉非字符串和非数字的参数值
  const filteredData = filterObjectByValue(data);
  // 按照字母顺序排序并生成签名字符串
  const signStr = Object.keys(filteredData)
    .sort()
    .map((key) => filteredData[key])
    .join("||");
  return md5(`${signStr}||${signkey}`);
};

/**
 * 过滤对象属性，仅保留值为有效数字或字符串的属性
 * @param obj 待过滤的对象
 * @param options 过滤选项
 * @returns 过滤后的新对象
 */
export function filterObjectByValue<T extends object>(
  obj: T,
  options: {
    // 是否递归处理嵌套对象
    recursive?: boolean;
    // 是否保留空字符串
    keepEmptyString?: boolean;
  } = {}
): Partial<T> {
  const { recursive = true, keepEmptyString = false } = options;
  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    // 处理数字类型（过滤NaN和Infinity）
    if (typeof value === "number") {
      if (Number.isFinite(value)) {
        result[key] = value;
      }
    }
    // 处理字符串类型
    else if (typeof value === "string") {
      if (keepEmptyString || value.trim() !== "") {
        result[key] = value;
      }
    }
    // 递归处理嵌套对象
    else if (recursive && typeof value === "object" && value !== null) {
      if (Array.isArray(value)) {
        // 处理数组
        const filteredArray = value.flatMap((item: any): (string | number)[] => {
          if (typeof item === "number") {
            return Number.isFinite(item) ? [item] : [];
          }
          if (typeof item === "string") {
            return keepEmptyString || item.trim() !== "" ? [item] : [];
          }
          return [];
        });

        if (filteredArray.length > 0) {
          result[key] = filteredArray;
        }
      } else {
        // 处理普通对象
        const nestedResult = filterObjectByValue(value, options);
        if (Object.keys(nestedResult).length > 0) {
          result[key] = nestedResult;
        }
      }
    }
  }

  return result as Partial<T>;
}

/**
 * 设备检测工具
 */
export const deviceDetector = {
  /**
   * 检测是否为iOS设备
   */
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  },

  /**
   * 检测是否为Android设备
   */
  isAndroid(): boolean {
    return /Android/.test(navigator.userAgent);
  },

  /**
   * 检测是否为移动设备
   */
  isMobile(): boolean {
    return this.isIOS() || this.isAndroid();
  },

  /**
   * 获取设备类型
   */
  getDeviceType(): "ios" | "android" | "desktop" {
    if (this.isIOS()) return "ios";
    if (this.isAndroid()) return "android";
    return "desktop";
  },

  /**
   * 检测是否在微信中
   */
  isWeChat(): boolean {
    return /MicroMessenger/i.test(navigator.userAgent);
  },

  /**
   * 检测是否在QQ中
   */
  isQQ(): boolean {
    return /QQ/i.test(navigator.userAgent);
  },
};

/**
 * 应用下载工具
 */
export const appDownloader = {
  /**
   * 尝试打开app，失败后跳转应用商城
   * @param storeUrl 应用商城链接
   * @param platform 平台类型
   * @param timeout 超时时间（毫秒）
   */
  async openApp(storeUrl: string, platform: "ios" | "android", timeout: number = 3000): Promise<boolean> {
    // 根据平台使用不同的唤起策略
    if (platform === "ios") {
      return this.openIOSApp(storeUrl, timeout);
    } else {
      return this.openAndroidApp(storeUrl, timeout);
    }
  },

  /**
   * iOS专用的app唤起方法
   * iOS使用iframe方式更稳定，避免页面跳转
   */
  async openIOSApp(appScheme: string, storeUrl: string, timeout: number = 3000): Promise<boolean> {
    return new Promise((resolve) => {
      let isResolved = false;
      let appOpenTimer: number;
      let iframe: HTMLIFrameElement | null = null;

      console.log("iOS: Attempting to open app with iframe method");

      // 检测iOS版本和浏览器类型
      const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
      const iosVersion = this.getIOSVersion();

      console.log(`iOS: Safari=${isSafari}, iOS version=${iosVersion}`);

      // iOS特有的检测方法：监听页面可见性变化
      const handleVisibilityChange = () => {
        if (!isResolved && document.visibilityState === "hidden") {
          clearTimeout(appOpenTimer);
          isResolved = true;
          console.log("iOS: App opened successfully (visibility change detected)");
          cleanup();
          resolve(true);
        }
      };

      // iOS在某些版本中，页面失焦也可能表示app打开
      const handlePageHide = () => {
        if (!isResolved) {
          clearTimeout(appOpenTimer);
          isResolved = true;
          console.log("iOS: App opened successfully (page hide detected)");
          cleanup();
          resolve(true);
        }
      };

      // iOS 9+支持的focus事件检测
      const handleFocus = () => {
        // 如果页面重新获得焦点，可能是用户从app返回
        setTimeout(() => {
          if (!isResolved) {
            console.log("iOS: Page regained focus, assuming app was opened");
            clearTimeout(appOpenTimer);
            isResolved = true;
            cleanup();
            resolve(true);
          }
        }, 100);
      };

      const cleanup = () => {
        if (iframe && iframe.parentNode) {
          iframe.parentNode.removeChild(iframe);
        }
        document.removeEventListener("visibilitychange", handleVisibilityChange);
        window.removeEventListener("pagehide", handlePageHide);
        window.removeEventListener("focus", handleFocus);
      };

      // 设置超时，如果app没有打开，则跳转应用商城
      const timeoutDuration = isSafari ? 3000 : 2500; // Safari给更长时间
      appOpenTimer = setTimeout(async () => {
        if (!isResolved) {
          console.log("iOS: App opening timeout, fallback to app store");
          cleanup();

          // 尝试打开应用商城
          const storeSuccess = await this.openAppStore(storeUrl, timeout);
          if (!isResolved) {
            isResolved = true;
            resolve(storeSuccess);
          }
        }
      }, timeoutDuration);

      try {
        // 添加事件监听器
        document.addEventListener("visibilitychange", handleVisibilityChange);
        window.addEventListener("pagehide", handlePageHide);
        window.addEventListener("focus", handleFocus);

        // iOS 9+可以尝试使用location.href，较老版本使用iframe
        if (iosVersion >= 9 && isSafari) {
          console.log("iOS: Using location.href method for iOS 9+ Safari");
          window.location.href = appScheme;
        } else {
          console.log("iOS: Using iframe method for compatibility");
          // iOS使用iframe方式尝试打开app
          iframe = document.createElement("iframe");
          iframe.style.display = "none";
          iframe.style.width = "1px";
          iframe.style.height = "1px";
          iframe.src = appScheme;
          document.body.appendChild(iframe);
        }

        console.log("iOS: App scheme initiated");

        // 清理事件监听器
        setTimeout(() => {
          cleanup();
        }, timeout);
      } catch (error) {
        console.error("iOS: Failed to open app scheme:", error);
        clearTimeout(appOpenTimer);
        cleanup();

        if (!isResolved) {
          // 如果打开app失败，尝试打开应用商城
          this.openAppStore(storeUrl, timeout).then(resolve);
        }
      }
    });
  },

  /**
   * 获取iOS版本号
   */
  getIOSVersion(): number {
    const match = navigator.userAgent.match(/OS (\d+)_/);
    return match ? parseInt(match[1], 10) : 0;
  },

  /**
   * Android专用的app唤起方法
   * Android使用location.href方式，并结合Intent fallback
   */
  async openAndroidApp(appScheme: string, storeUrl: string, timeout: number = 3000): Promise<boolean> {
    return new Promise((resolve) => {
      let isResolved = false;
      let appOpenTimer: number;

      // 检测Android浏览器类型
      const isChrome = /Chrome/.test(navigator.userAgent);
      const androidVersion = this.getAndroidVersion();

      console.log(`Android: Attempting to open app, Chrome=${isChrome}, Android version=${androidVersion}`);

      // Android的检测方法：监听页面可见性变化和窗口失焦
      const handleVisibilityChange = () => {
        if (!isResolved && document.visibilityState === "hidden") {
          clearTimeout(appOpenTimer);
          isResolved = true;
          console.log("Android: App opened successfully (visibility change detected)");
          cleanup();
          resolve(true);
        }
      };

      const handleBlur = () => {
        if (!isResolved) {
          clearTimeout(appOpenTimer);
          isResolved = true;
          console.log("Android: App opened successfully (window blur detected)");
          cleanup();
          resolve(true);
        }
      };

      // Android特有：监听beforeunload事件
      const handleBeforeUnload = () => {
        if (!isResolved) {
          clearTimeout(appOpenTimer);
          isResolved = true;
          console.log("Android: App opened successfully (page unload detected)");
          cleanup();
          resolve(true);
        }
      };

      const cleanup = () => {
        document.removeEventListener("visibilitychange", handleVisibilityChange);
        window.removeEventListener("blur", handleBlur);
        window.removeEventListener("beforeunload", handleBeforeUnload);
      };

      // 设置超时，如果app没有打开，则跳转应用商城
      appOpenTimer = setTimeout(async () => {
        if (!isResolved) {
          console.log("Android: App opening timeout, fallback to app store");
          cleanup();

          // Android可以尝试使用Intent URL作为fallback
          const intentUrl = this.createAndroidIntentUrl(appScheme, storeUrl);
          if (intentUrl) {
            try {
              window.location.href = intentUrl;
              console.log("Android: Intent URL fallback initiated");
              setTimeout(() => {
                if (!isResolved) {
                  isResolved = true;
                  resolve(true);
                }
              }, 1000);
              return;
            } catch (intentError) {
              console.log("Android: Intent URL failed, using app store");
            }
          }

          // 最后fallback到应用商城
          const storeSuccess = await this.openAppStore(storeUrl, timeout);
          if (!isResolved) {
            isResolved = true;
            resolve(storeSuccess);
          }
        }
      }, 2000); // Android给较短的时间

      try {
        // 添加事件监听器
        document.addEventListener("visibilitychange", handleVisibilityChange);
        window.addEventListener("blur", handleBlur);
        window.addEventListener("beforeunload", handleBeforeUnload);

        // 根据浏览器和Android版本选择最佳策略
        if (isChrome && androidVersion >= 6) {
          // Chrome 25+ 和 Android 6+ 支持Intent URL
          const intentUrl = this.createAndroidIntentUrl(appScheme, storeUrl);
          if (intentUrl) {
            console.log("Android: Using Intent URL for Chrome");
            window.location.href = intentUrl;
          } else {
            console.log("Android: Fallback to scheme URL");
            window.location.href = appScheme;
          }
        } else {
          // 其他浏览器或较老版本使用传统scheme方式
          console.log("Android: Using traditional scheme URL");
          window.location.href = appScheme;
        }

        console.log("Android: App scheme redirect initiated");

        // 清理事件监听器
        setTimeout(() => {
          cleanup();
        }, timeout);
      } catch (error) {
        console.error("Android: Failed to open app scheme:", error);
        clearTimeout(appOpenTimer);
        cleanup();

        if (!isResolved) {
          // 如果打开app失败，尝试打开应用商城
          this.openAppStore(storeUrl, timeout).then(resolve);
        }
      }
    });
  },

  /**
   * 创建Android Intent URL
   * @param appScheme app scheme
   * @param storeUrl 应用商城链接
   */
  createAndroidIntentUrl(appScheme: string, storeUrl: string): string | null {
    try {
      // 从storeUrl中提取包名
      const packageMatch = storeUrl.match(/id=([^&]+)/);
      if (!packageMatch) return null;

      const packageName = packageMatch[1];

      // 构建Intent URL
      return `intent://${appScheme.replace(/^[^:]+:\/\//, "")}#Intent;scheme=${appScheme.split("://")[0]};package=${packageName};S.browser_fallback_url=${encodeURIComponent(storeUrl)};end`;
    } catch (error) {
      console.error("Failed to create Android Intent URL:", error);
      return null;
    }
  },

  /**
   * 获取Android版本号
   */
  getAndroidVersion(): number {
    const match = navigator.userAgent.match(/Android (\d+)/);
    return match ? parseInt(match[1], 10) : 0;
  },

  /**
   * 尝试打开应用商城
   * @param storeUrl 应用商城链接
   * @param timeout 超时时间（毫秒）
   */
  async openAppStore(storeUrl: string, timeout: number = 3000): Promise<boolean> {
    return new Promise((resolve) => {
      let isResolved = false;

      console.log("Attempting to open URL:", storeUrl);

      // 设置一个较短的成功超时 - 假设跳转成功
      const successTimer = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          console.log("URL opened successfully (assumed success)");
          resolve(true);
        }
      }, 1500);

      // 设置失败超时 - 只有在明确检测到失败时才使用
      const failureTimer = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          console.log("URL opening failed (timeout)");
          resolve(false);
        }
      }, timeout);

      try {
        // 尝试打开链接
        window.location.href = storeUrl;
        console.log("URL redirect initiated");

        // 监听页面可见性变化 - 如果页面变为隐藏，说明跳转成功
        const handleVisibilityChange = () => {
          if (!isResolved && document.visibilityState === "hidden") {
            clearTimeout(successTimer);
            clearTimeout(failureTimer);
            isResolved = true;
            console.log("URL opened successfully (visibility change detected)");
            resolve(true);
          }
        };

        // 监听页面卸载事件 - 如果页面卸载，说明跳转成功
        const handleBeforeUnload = () => {
          if (!isResolved) {
            clearTimeout(successTimer);
            clearTimeout(failureTimer);
            isResolved = true;
            console.log("URL opened successfully (page unload detected)");
            resolve(true);
          }
        };

        // 监听窗口失去焦点 - 可能是跳转到了应用商店
        const handleBlur = () => {
          if (!isResolved) {
            clearTimeout(successTimer);
            clearTimeout(failureTimer);
            isResolved = true;
            console.log("URL opened successfully (window blur detected)");
            resolve(true);
          }
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);
        window.addEventListener("beforeunload", handleBeforeUnload);
        window.addEventListener("blur", handleBlur);

        // 清理事件监听器
        setTimeout(() => {
          document.removeEventListener("visibilitychange", handleVisibilityChange);
          window.removeEventListener("beforeunload", handleBeforeUnload);
          window.removeEventListener("blur", handleBlur);
        }, timeout);
      } catch (error) {
        console.error("Failed to open URL:", error);
        clearTimeout(successTimer);
        clearTimeout(failureTimer);
        if (!isResolved) {
          isResolved = true;
          resolve(false);
        }
      }
    });
  },

  /**
   * 显示Toast提示
   * @param message 提示信息
   * @param duration 显示时长（毫秒）
   */
  showToast(message: string, duration: number = 3000): void {
    // 移除已存在的toast
    const existingToast = document.querySelector(".app-download-toast");
    if (existingToast) {
      existingToast.remove();
    }

    // 创建toast元素
    const toast = document.createElement("div");
    toast.className = "app-download-toast";
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 9999;
      max-width: 80%;
      text-align: center;
      word-wrap: break-word;
    `;

    // 添加到页面
    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, duration);
  },
};
