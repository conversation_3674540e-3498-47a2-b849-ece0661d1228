<script setup lang="ts" name="NotSupported">
import { onBeforeMount, ref } from "vue";
import { getIOSJumpLink } from "../../service";

// 定义配置数据类型
interface OopsConfig {
  classify: string;
  identifier: string;
  name: string;
  value: string;
}

const oopsConfig = ref<OopsConfig | null>(null);
// 获取ios配置链接
const getIosOopsConfig = async () => {
  const res = await getIOSJumpLink();
  console.log("iosConfig", res);
  if (res.code === 0 && res.data.value?.length > 0) {
    oopsConfig.value = res.data.value[0];
  }
};

onBeforeMount(() => {
  getIosOopsConfig();
});
// 访问网站
function visitWebsite() {
  // 这里可以配置实际的网站URL
  if (oopsConfig.value?.value) {
    window.open(oopsConfig.value.value, "_blank");
  }
}
</script>

<template>
  <div class="not-supported-page">
    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 主标题 -->
      <h1 class="main-title">Oops!</h1>

      <!-- 提示信息 -->
      <p class="message">Sorry, our app aren't available in IOS yet.</p>

      <!-- 访问说明 -->
      <p class="instruction">Visit Nustar by clicking below URL.</p>

      <!-- 网站链接 -->
      <a href="#" @click.prevent="visitWebsite" v-if="oopsConfig?.value" class="website-link">{{ oopsConfig?.value || "Visit Website" }}</a>
    </div>

    <!-- 返回按钮 -->
  </div>
</template>

<style lang="scss" scoped>
.not-supported-page {
  font-family: "Inter";
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.content-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0px;
  text-align: center;
  width: 100%;
}

.main-title {
  font-size: 48px;
  font-weight: 700;
  color: #333;
  margin: 0 0 40px 0;
  letter-spacing: -0.02em;
}

.message {
  font-size: 18px;
  font-weight: 500;
  color: #555;
  margin: 0 0 30px 0;
  line-height: 1.4;
}

.instruction {
  font-size: 16px;
  font-weight: 400;
  color: #666;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.website-link {
  font-size: 18px;
  font-weight: 600;
  color: #007aff;
  text-decoration: underline;
}
</style>
